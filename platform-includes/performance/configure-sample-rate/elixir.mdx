Tracing will be available in a future version of the Sentry Elixir SDK.

```elixir {filename:config/config.exs}
config :sentry,
  dsn: "___PUBLIC_DSN___",
  # Set a uniform sample rate between 0.0 and 1.0
  # We recommend adjusting the value in production:
  traces_sample_rate: 1.0,

  # or control sampling dynamically
  traces_sampler: fn sampling_context ->
    # sampling_context[:transaction_context] contains the information about the transaction
    # sampling_context[:parent_sampled] contains the transaction's parent's sample decision
    true # return value can be a boolean or a float between 0.0 and 1.0
  end
```
