---
title: Options
description: "Learn more about how the SDK can be configured via options. These are being passed to the init function and therefore set when the SDK is first initialized."
sidebar_order: 1
---

<PlatformContent includePath="configuration/config-intro" />

## Available Options

<TableOfContents ignoreIds={["available-options"]} />

## Core Options

Options that can be read from an environment variable (`SENTRY_DSN`, `SENTRY_ENVIRONMENT`, `SENTRY_RELEASE`) are read automatically.

<SdkOption name="dsn" type='string' envVar='SENTRY_DSN'>

The DSN tells the SDK where to send the events. If this value is not provided, the SDK will try to read it from the `SENTRY_DSN` environment variable. If that variable also does not exist, the SDK will just not send any events.

Learn more about [DSN utilization](/product/sentry-basics/dsn-explainer/#dsn-utilization).

</SdkOption>

<SdkOption name="release" type='string' envVar='SENTRY_RELEASE'>

Sets the release. Some SDKs will try to automatically configure a release out of the box but it's a better idea to manually set it to guarantee that the release is in sync with your deploy integrations or source map uploads. Release names are strings, but some formats are detected by Sentry and might be rendered differently. Learn more about how to send release data so Sentry can tell you about regressions between releases and identify the potential source in [the releases documentation](/product/releases/) or the <SandboxLink scenario="releases">sandbox</SandboxLink>.

By default the SDK will try to read this value from the `SENTRY_RELEASE` environment variable.

</SdkOption>

<SdkOption name="environment" type='string' envVar='SENTRY_ENVIRONMENT'>

Sets the environment. This string is freeform and not set by default. A release can be associated with more than one environment to separate them in the UI (think `staging` vs `prod` or similar).

By default the SDK will try to read this value from the `SENTRY_ENVIRONMENT` environment variable.

</SdkOption>

<SdkOption name="sample_rate" type='float' defaultValue='1.0'>

Configures the sample rate for error events, in the range of `0.0` to `1.0`. The default is `1.0`, which means that 100% of error events will be sent. If set to `0.1`, only 10% of error events will be sent. Events are picked randomly.

</SdkOption>

<SdkOption name="max_breadcrumbs" type='integer' defaultValue='100'>

This variable controls the total amount of breadcrumbs that should be captured. This defaults to `100`, but you can set this to any number. However, you should be aware that Sentry has a [maximum payload size](https://develop.sentry.dev/sdk/data-model/envelopes/#size-limits) and any events exceeding that payload size will be dropped.

</SdkOption>

<SdkOption name="server_name" type='string'>

This option can be used to supply a server name. When provided, the name of the server is sent along and persisted in the event. For many integrations, the server name actually corresponds to the device hostname, even in situations where the machine is not actually a server.

Most SDKs will attempt to auto-discover this value.

</SdkOption>

## Hooks

These options can be used to hook the SDK in various ways to customize the reporting of events.

<SdkOption name="before_send" type='function'>

This function is called with an SDK-specific message or error event object, and can return a modified event object, or `null` to skip reporting the event. This can be used, for instance, for manual PII stripping before sending.

By the time <PlatformIdentifier name="before_send" /> is executed, all scope data has already been applied to the event. Further modification of the scope won't have any effect.

</SdkOption>

<SdkOption name="after_send_event" type='function'>

This function is called with an event and the result of sending that event. This is useful to log send results, instrument Sentry calls, and so on.

</SdkOption>

## Transport Options

Transports are used to send events to Sentry. Transports can be customized to some degree to better support highly specific deployments.

<SdkOption name="send_result" defaultValue=':none'>

Controls whether to report events to Sentry _synchronously_ (if set to `:sync`) or _asynchronously_ (if set to `:none`).

</SdkOption>

<SdkOption name="send_max_attempts" type='integer' defaultValue='4'>

The maximum number of attempts to send an event to Sentry.

</SdkOption>

<SdkOption name="client" defaultValue='Sentry.HackneyClient'>

The HTTP client to use for sending events to Sentry. This must be a module that implements the [`Sentry.HTTPClient`](https://hexdocs.pm/sentry/Sentry.HTTPClient.html) behaviour. Defaults to `Sentry.HackneyClient`, which is based on the [Hackney](https://hexdocs.pm/hackney) HTTP client.

</SdkOption>

<SdkOption name="hackney_opts" defaultValue='[pool: :sentry_pool]'>

Options to be passed to Hackney. Only applied if `client` is set to `Sentry.HackneyClient`.

</SdkOption>

<SdkOption name="hackney_pool_max_connections" type='integer' defaultValue='50'>

The maximum number of connections to keep in the pool. Only applied if `client` is set to `Sentry.HackneyClient`.

</SdkOption>

<SdkOption name="hackney_pool_timeout" type='integer' defaultValue='5000'>

The maximum time to wait (in milliseconds) for a connection to become available. Only applied if `client` is set to `Sentry.HackneyClient`.

</SdkOption>

## Tracing Options

<SdkOption name="traces_sample_rate" type='float' defaultValue='nil'>

A number between `0.0` and `1.0` that determines the percentage of transactions that will be sent to Sentry. Either this or `traces_sampler` must be defined to enable tracing.

```elixir
config :sentry,
  traces_sample_rate: 0.2  # Sample 20% of transactions
```

</SdkOption>

<SdkOption name="traces_sampler" type='function'>

A function that determines the sample rate for transaction events. This function receives a sampling context map and should return a boolean or a float between `0.0` and `1.0`.

```elixir
config :sentry,
  traces_sampler: fn sampling_context ->
    case sampling_context.transaction_context.op do
      "http.server" -> 0.1  # Sample 10% of HTTP requests
      _ -> 0.05             # Sample 5% of other operations
    end
  end
```

If both `:traces_sampler` and `:traces_sample_rate` are configured, `:traces_sampler` takes precedence.

</SdkOption>
