---
title: Set Up Tracing
sidebar_title: Tracing
description: "Learn how to enable tracing in your app and discover valuable performance insights of your application."
sidebar_order: 30
---

With [tracing](/product/insights/overview/), Sentry tracks your software performance, measuring metrics like throughput and latency, and displaying the impact of errors across multiple systems.

## Configure

To enable tracing, add the sample rate to your Sentry configuration:

```elixir
config :sentry,
  dsn: "___PUBLIC_DSN___",
  traces_sample_rate: 1.0
```

For more control over sampling, you can use a sampling function:

```elixir
config :sentry,
  dsn: "___PUBLIC_DSN___",
  traces_sampler: fn sampling_context ->
    case sampling_context.transaction_context.op do
      "http.server" -> 0.1  # Sample 10% of HTTP requests
      _ -> 0.05             # Sample 5% of other operations
    end
  end
```

Learn more about tracing <PlatformLink to="/configuration/options/#tracing-options">options</PlatformLink>.
