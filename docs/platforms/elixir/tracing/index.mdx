---
title: Set Up Tracing
sidebar_title: Tracing
description: "Learn how to enable tracing in your app and discover valuable performance insights of your application."
sidebar_order: 4000
---

With [tracing](/product/insights/overview/), Sentry tracks your software performance, measuring metrics like throughput and latency, and displaying the impact of errors across multiple systems. Sentry captures distributed traces consisting of transactions and spans, which measure individual services and individual operations within those services. Learn more about our model in [Distributed Tracing](/product/sentry-basics/tracing/distributed-tracing/).

<Alert>

Tracing for Elixir is currently in development. This documentation describes the planned API and functionality that will be available in a future release of the Sentry Elixir SDK.

</Alert>

## Configure

First, enable tracing in your `Sentry.init` configuration:

<PlatformContent includePath="performance/configure-sample-rate" />

Learn more about tracing <PlatformLink to="/configuration/options/#tracing-options">options</PlatformLink>, how to use the <PlatformLink to="/configuration/sampling/#setting-a-sampling-function">traces_sampler</PlatformLink> function, or how to <PlatformLink to="/configuration/sampling/#sampling-transaction-events">sample transactions</PlatformLink>.

## Verify

Test out tracing by starting and finishing a transaction, which you _must_ do so transactions can be sent to Sentry. Learn how in our <PlatformLink to="/tracing/instrumentation/custom-instrumentation/">Custom Instrumentation</PlatformLink> content.

## Next Steps

<PageGrid />
