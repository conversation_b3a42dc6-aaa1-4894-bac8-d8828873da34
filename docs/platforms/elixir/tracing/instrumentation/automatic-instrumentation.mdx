---
title: Automatic Instrumentation
sidebar_order: 10
supported:
  - elixir
description: "Learn what instrumentation automatically captures transactions."
---

<Alert>

Automatic instrumentation for Elixir is currently in development. This documentation describes the planned functionality that will be available in a future release of the Sentry Elixir SDK.

</Alert>

Many integrations for popular frameworks will automatically capture transactions. If you have any of the following frameworks set up for Sentry error reporting and use the relevant Sentry integration, you will start to see traces immediately:

- Phoenix (`sentry` with Phoenix integration)
- Oban (`sentry` with Oban integration)
- Quantum (`sentry` with Quantum integration)
- Plug applications (`sentry` with Plug integration)

Spans will be instrumented for the following operations within a transaction:

- Database queries using Ecto
  - includes common database systems such as PostgreSQL and MySQL
- Outgoing HTTP requests made with common HTTP clients
  - HTTPoison
  - Finch
  - Tesla
- Redis operations
- Phoenix LiveView events
- GenServer calls

Spans are only created within an existing transaction. If you're not using any of the supported frameworks, you'll need to <PlatformLink to="/tracing/instrumentation/custom-instrumentation/">create transactions manually</PlatformLink>.

## Phoenix

For Phoenix applications, transactions are automatically created for:

- HTTP requests (controller actions)
- LiveView mount and event handling
- Channel joins and message handling

## Oban

For Oban job processing, transactions are automatically created for:

- Job execution
- Job retries
- Scheduled job processing

## Plug

For Plug applications, transactions are automatically created for:

- HTTP request processing through the Plug pipeline

## Ecto

Database operations are automatically instrumented when using Ecto, including:

- Query execution
- Transaction blocks
- Migration operations
