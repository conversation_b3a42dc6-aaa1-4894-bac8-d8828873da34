<.header>
  Listing Users
  <:actions>
    <.link patch={~p"/users/new"}>
      <.button>New User</.button>
    </.link>
  </:actions>
</.header>

<.table
  id="users"
  rows={@streams.users}
  row_click={fn {_id, user} -> JS.navigate(~p"/users/#{user}") end}
>
  <:col :let={{_id, user}} label="Name"><%= user.name %></:col>
  <:col :let={{_id, user}} label="Age"><%= user.age %></:col>
  <:action :let={{_id, user}}>
    <div class="sr-only">
      <.link navigate={~p"/users/#{user}"}>Show</.link>
    </div>
    <.link patch={~p"/users/#{user}/edit"}>Edit</.link>
  </:action>
  <:action :let={{id, user}}>
    <.link
      phx-click={JS.push("delete", value: %{id: user.id}) |> hide("##{id}")}
      data-confirm="Are you sure?"
    >
      Delete
    </.link>
  </:action>
</.table>

<.modal :if={@live_action in [:new, :edit]} id="user-modal" show on_cancel={JS.patch(~p"/users")}>
  <.live_component
    module={PhoenixAppWeb.UserLive.FormComponent}
    id={@user.id || :new}
    title={@page_title}
    action={@live_action}
    user={@user}
    patch={~p"/users"}
  />
</.modal>
