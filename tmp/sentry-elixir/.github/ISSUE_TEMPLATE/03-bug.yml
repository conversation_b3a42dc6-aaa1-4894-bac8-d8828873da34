name: 🐞 Bug Report
description: Report an unexpected problem or behavior of this SDK.
labels: ["<PERSON>xir", "Bug"]
body:
  - type: textarea
    attributes:
      label: Issue Description
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction Steps
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual Behavior
    validations:
      required: true
  - type: input
    attributes:
      label: Elixir Version
    validations:
      required: true
  - type: input
    attributes:
      label: SDK Version
    validations:
      required: true
  - type: input
    attributes:
      label: Integration and Its Version
      description: e.g. Rails/Sidekiq/Rake/DelayedJob...etc.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Sentry Config
    validations:
      required: false
